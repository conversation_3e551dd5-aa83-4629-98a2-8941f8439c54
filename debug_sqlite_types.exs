#!/usr/bin/env elixir

# Debug script to see what SQLite column types look like when introspected
Mix.install([
  {:ecto, "~> 3.10"},
  {:ecto_sqlite3, "~> 0.12"}
])

defmodule DebugRepo do
  use Ecto.Repo,
    otp_app: :debug,
    adapter: Ecto.Adapters.SQLite3
end

Application.put_env(:debug, DebugRepo, database: ":memory:")

{:ok, _} = DebugRepo.start_link()

# Create a test table with various date/datetime column types
DebugRepo.query!("""
CREATE TABLE test_dates (
  id INTEGER PRIMARY KEY,
  date_col DATE,
  datetime_col DATETIME,
  timestamp_col TIMESTAMP,
  time_col TIME,
  text_col TEXT,
  integer_col INTEGER
)
""")

# Wait a moment for the table to be created
Process.sleep(100)

# Use PRAGMA table_info to see what SQLite reports for column types
result = DebugRepo.query!("PRAGMA table_info(test_dates)")

IO.puts("SQLite PRAGMA table_info results:")
IO.puts("Columns: #{inspect(result.columns)}")
IO.puts("Rows:")
Enum.each(result.rows, fn row ->
  IO.puts("  #{inspect(row)}")
end)

IO.puts("\nFormatted column info:")
Enum.each(result.rows, fn [_cid, name, type, notnull, default_value, pk] ->
  IO.puts("Column: #{name}")
  IO.puts("  Type: #{inspect(type)}")
  IO.puts("  Not Null: #{notnull}")
  IO.puts("  Default: #{inspect(default_value)}")
  IO.puts("  Primary Key: #{pk}")
  IO.puts("")
end)
