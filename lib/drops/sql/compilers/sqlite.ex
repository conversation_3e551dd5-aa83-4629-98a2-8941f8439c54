defmodule Drops.SQL.Compilers.Sqlite do
  @moduledoc """
  SQLite-specific compiler for processing database introspection ASTs.

  This module implements the `Drops.SQL.Compiler` behavior to provide SQLite-specific
  type mapping and AST processing. It converts SQLite database types to Ecto types
  and handles SQLite-specific type characteristics.

  ## SQLite Type System

  SQLite uses a dynamic type system with type affinity rather than strict types.
  This compiler maps SQLite's type affinities to appropriate Ecto types:

  ### Numeric Types
  - `INTEGER` → `:integer`
  - `REAL`, `FLOAT` → `:float`
  - `NUMERIC`, `DECIMAL` → `:decimal`

  ### Text Types
  - `TEXT` → `:string`
  - Character types (VARCHAR, CHAR, etc.) → `:string`

  ### Binary Types
  - `BLOB` → `:binary`

  ### Boolean Types
  - `BOOLEAN`, `BOOL` → `:boolean`

  ### Date/Time Types
  - `DATE` → `:date`
  - `TIME` → `:time`
  - `DATETIME`, `TIM<PERSON><PERSON><PERSON>` → `:naive_datetime`

  ### Special Types
  - `UUID` → `:uuid`
  - `JSON` → `:map`

  ## Type Affinity Rules

  SQLite's type affinity rules are respected:
  - Types containing "INT" are mapped to `:integer`
  - Types containing "CHAR", "CLOB", or "TEXT" are mapped to `:string`
  - Types containing "BLOB" or no affinity are mapped to `:binary`
  - Types containing "REAL", "FLOA", or "DOUB" are mapped to `:float`

  ## Usage

  This compiler is typically used automatically by the `Drops.SQL.Sqlite` adapter:

      # Automatic usage through adapter
      {:ok, table} = Drops.SQL.Sqlite.table("users", MyRepo)

      # Direct usage (advanced)
      ast = {:table, {{:identifier, "users"}, columns, [], []}}
      table = Drops.SQL.Compilers.Sqlite.process(ast, adapter: :sqlite)

  ## Implementation Notes

  - Case-insensitive type matching (SQLite is case-insensitive)
  - Handles both exact type names and type affinity patterns
  - Preserves unknown types as-is for custom handling
  - Supports SQLite's flexible typing system
  """

  use Drops.SQL.Compiler

  @doc """
  Visits a type AST node and maps SQLite types to Ecto types.

  This function implements SQLite-specific type mapping, handling SQLite's
  dynamic type system and type affinity rules.

  ## Parameters

  - `{:type, type}` - Type AST node with SQLite type name
  - `opts` - Processing options including column metadata for enhanced type detection

  ## Returns

  Ecto type atom (`:integer`, `:string`, etc.) or the original type if unmapped.

  ## Examples

      iex> Drops.SQL.Compilers.Sqlite.visit({:type, "INTEGER"}, [])
      :integer

      iex> Drops.SQL.Compilers.Sqlite.visit({:type, "TEXT"}, [])
      :string

      iex> Drops.SQL.Compilers.Sqlite.visit({:type, "BLOB"}, [])
      :binary
  """
  @spec visit({:type, String.t()}, keyword()) :: atom() | String.t()
  def visit({:type, type}, opts) do
    normalized_type = String.upcase(type)

    case normalized_type do
      "INTEGER" ->
        :integer

      "FLOAT" ->
        :float

      "TEXT" ->
        # For TEXT columns, we need to infer the actual type based on context
        infer_text_column_type(opts)

      "REAL" ->
        :float

      "BLOB" ->
        :binary

      "UUID" ->
        :uuid

      type when type in ["NUMERIC", "DECIMAL"] ->
        :decimal

      type when type in ["BOOLEAN", "BOOL"] ->
        :boolean

      type when type in ["DATETIME", "TIMESTAMP"] ->
        :naive_datetime

      "DATE" ->
        :date

      "TIME" ->
        :time

      "JSON" ->
        :map
    end
  end

  def visit({:default, nil}, _opts), do: nil
  def visit({:default, ""}, _opts), do: nil

  @doc """
  Infers the actual Ecto type for TEXT columns in SQLite based on context clues.

  Since SQLite stores date/datetime types as TEXT, we need to use naming conventions
  and default values to infer the original intended type.

  ## Context Clues Used:

  1. **Column naming patterns**:
     - `*_at` (e.g., `created_at`, `updated_at`) → `:naive_datetime`
     - `*_date` (e.g., `birth_date`, `start_date`) → `:date`
     - `*_time` (e.g., `start_time`) → `:time`
     - `inserted_at`, `updated_at` → `:naive_datetime`

  2. **Default values**:
     - `CURRENT_TIMESTAMP` → `:naive_datetime`
     - `CURRENT_DATE` → `:date`
     - `CURRENT_TIME` → `:time`

  3. **Fallback**: `:string` for all other TEXT columns
  """
  @spec infer_text_column_type(keyword()) :: atom()
  defp infer_text_column_type(opts) do
    column_name = get_column_name_from_opts(opts)
    default_value = get_default_value_from_opts(opts)

    cond do
      # Check default values first (most reliable)
      default_value == :current_timestamp -> :naive_datetime
      default_value == :current_date -> :date
      default_value == :current_time -> :time
      # Check naming patterns
      is_datetime_column?(column_name) -> :naive_datetime
      is_date_column?(column_name) -> :date
      is_time_column?(column_name) -> :time
      # Default to string for other TEXT columns
      true -> :string
    end
  end

  # Extract column name from compiler options
  @spec get_column_name_from_opts(keyword()) :: String.t() | nil
  defp get_column_name_from_opts(opts) do
    # The column name should be available in the opts when processing a column
    # This will be passed down from the SQL.Compiler when visiting column nodes
    Keyword.get(opts, :column_name)
  end

  # Extract default value from compiler options
  @spec get_default_value_from_opts(keyword()) :: term()
  defp get_default_value_from_opts(opts) do
    Keyword.get(opts, :default_value)
  end

  # Check if column name suggests it's a datetime column
  @spec is_datetime_column?(String.t() | nil) :: boolean()
  defp is_datetime_column?(nil), do: false

  defp is_datetime_column?(name) when is_binary(name) do
    name = String.downcase(name)

    String.ends_with?(name, "_at") or
      name in ["inserted_at", "updated_at", "created_at", "deleted_at"] or
      String.contains?(name, "datetime") or
      String.contains?(name, "timestamp")
  end

  defp is_datetime_column?(name) when is_atom(name) do
    is_datetime_column?(Atom.to_string(name))
  end

  # Check if column name suggests it's a date column
  @spec is_date_column?(String.t() | nil) :: boolean()
  defp is_date_column?(nil), do: false

  defp is_date_column?(name) when is_binary(name) do
    name = String.downcase(name)

    String.ends_with?(name, "_date") or
      (String.contains?(name, "date") and not String.contains?(name, "datetime"))
  end

  defp is_date_column?(name) when is_atom(name) do
    is_date_column?(Atom.to_string(name))
  end

  # Check if column name suggests it's a time column
  @spec is_time_column?(String.t() | nil) :: boolean()
  defp is_time_column?(nil), do: false

  defp is_time_column?(name) when is_binary(name) do
    name = String.downcase(name)

    String.ends_with?(name, "_time") or
      (String.contains?(name, "time") and not String.contains?(name, "datetime"))
  end

  defp is_time_column?(name) when is_atom(name) do
    is_time_column?(Atom.to_string(name))
  end

  def visit({:default, value}, _opts) when is_binary(value) do
    trimmed =
      value
      |> String.trim()
      |> String.trim("'")
      |> String.trim("\"")

    cond do
      trimmed == "NULL" ->
        nil

      trimmed == "CURRENT_TIMESTAMP" ->
        :current_timestamp

      trimmed == "CURRENT_DATE" ->
        :current_date

      trimmed == "CURRENT_TIME" ->
        :current_time

      String.match?(trimmed, ~r/^\d+$/) ->
        String.to_integer(trimmed)

      String.match?(trimmed, ~r/^\d+\.\d+$/) ->
        String.to_float(trimmed)

      String.downcase(trimmed) in ["true", "false"] ->
        String.to_existing_atom(String.downcase(trimmed))

      true ->
        trimmed
    end
  end
end
