#!/usr/bin/env elixir

# Debug script to see what <PERSON>ct<PERSON> actually generates for date/datetime types
Mix.install([
  {:ecto, "~> 3.10"},
  {:ecto_sqlite3, "~> 0.12"}
])

defmodule DebugRepo do
  use Ecto.Repo,
    otp_app: :debug,
    adapter: Ecto.Adapters.SQLite3
end

Application.put_env(:debug, DebugRepo, database: ":memory:")

{:ok, _} = DebugRepo.start_link()

# Let's check what SQL Ecto would generate for different types
# by looking at the adapter's type mapping

IO.puts("=== Ecto SQLite3 Type Mappings ===")

# Check what types Ecto.Adapters.SQLite3 maps to
types_to_test = [:date, :naive_datetime, :time, :string, :text, :integer, :boolean]

Enum.each(types_to_test, fn ecto_type ->
  try do
    # This is how we can see what SQL type Ecto would use
    sql_type = Ecto.Adapters.SQLite3.loaders(ecto_type, ecto_type)
    IO.puts("#{ecto_type} -> #{inspect(sql_type)}")
  rescue
    e -> IO.puts("#{ecto_type} -> ERROR: #{inspect(e)}")
  end
end)

# Let's manually create a table with the types we want to test
IO.puts("\n=== Creating Test Table Manually ===")

DebugRepo.query!("""
CREATE TABLE test_types (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  date_col DATE,
  datetime_col DATETIME,
  timestamp_col TIMESTAMP,
  time_col TIME,
  string_col TEXT,
  text_col TEXT,
  inserted_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
)
""")

# Check if table exists
IO.puts("\n=== Checking if table exists ===")
tables_result = DebugRepo.query!("SELECT name FROM sqlite_master WHERE type='table'")
IO.puts("Tables: #{inspect(tables_result.rows)}")

# Now check what was actually created
IO.puts("\n=== PRAGMA table_info ===")
result = DebugRepo.query!("PRAGMA table_info(test_types)")
IO.puts("Columns: #{inspect(result.columns)}")
IO.puts("Rows: #{inspect(result.rows)}")
if result.rows != [] do
  Enum.each(result.rows, fn [_cid, name, type, notnull, default_value, pk] ->
    IO.puts("#{name}: #{type} (notnull: #{notnull}, default: #{inspect(default_value)}, pk: #{pk})")
  end)
else
  IO.puts("No rows returned - table might not exist or have no columns")
end

# Check the actual table schema
IO.puts("\n=== Table Schema ===")
schema_result = DebugRepo.query!("SELECT sql FROM sqlite_master WHERE type='table' AND name='test_types'")
if schema_result.rows != [] do
  [sql] = List.first(schema_result.rows)
  IO.puts(sql)
end
